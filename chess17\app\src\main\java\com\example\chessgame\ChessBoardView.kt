package com.example.chessgame

import android.content.Context
import android.graphics.*
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import androidx.core.content.ContextCompat

class ChessBoardView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val chessGame = ChessGame()
    private var squareSize = 0f
    private var boardSize = 0f
    private var offsetX = 0f
    private var offsetY = 0f
    
    // Paints for drawing
    private val lightSquarePaint = Paint().apply {
        color = ContextCompat.getColor(context, R.color.light_square)
        style = Paint.Style.FILL
    }
    
    private val darkSquarePaint = Paint().apply {
        color = ContextCompat.getColor(context, R.color.dark_square)
        style = Paint.Style.FILL
    }
    
    private val selectedSquarePaint = Paint().apply {
        color = ContextCompat.getColor(context, R.color.selected_square)
        style = Paint.Style.FILL
        alpha = 128
    }
    
    private val possibleMovePaint = Paint().apply {
        color = ContextCompat.getColor(context, R.color.possible_move)
        style = Paint.Style.FILL
        alpha = 128
    }
    
    private val checkSquarePaint = Paint().apply {
        color = ContextCompat.getColor(context, R.color.check_square)
        style = Paint.Style.FILL
        alpha = 128
    }
    
    private val textPaint = Paint().apply {
        color = Color.BLACK
        textAlign = Paint.Align.CENTER
        isAntiAlias = true
        typeface = Typeface.DEFAULT_BOLD
    }
    
    private val borderPaint = Paint().apply {
        color = Color.BLACK
        style = Paint.Style.STROKE
        strokeWidth = 2f
    }
    
    // Callbacks for game events
    var onGameStateChanged: ((String) -> Unit)? = null
    var onMoveCompleted: (() -> Unit)? = null
    
    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        
        // Calculate board size (square)
        boardSize = minOf(w.toFloat(), h.toFloat()) * 0.9f
        squareSize = boardSize / 8f
        
        // Center the board
        offsetX = (w - boardSize) / 2f
        offsetY = (h - boardSize) / 2f
        
        // Update text size based on square size
        textPaint.textSize = squareSize * 0.7f
    }
    
    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        
        drawBoard(canvas)
        drawHighlights(canvas)
        drawPieces(canvas)
        drawBorder(canvas)
    }
    
    private fun drawBoard(canvas: Canvas) {
        for (row in 0..7) {
            for (col in 0..7) {
                val left = offsetX + col * squareSize
                val top = offsetY + row * squareSize
                val right = left + squareSize
                val bottom = top + squareSize
                
                val paint = if ((row + col) % 2 == 0) lightSquarePaint else darkSquarePaint
                canvas.drawRect(left, top, right, bottom, paint)
            }
        }
    }
    
    private fun drawHighlights(canvas: Canvas) {
        // Highlight selected square
        chessGame.getSelectedPosition()?.let { position ->
            drawSquareHighlight(canvas, position, selectedSquarePaint)
            
            // Highlight possible moves
            chessGame.getPossibleMoves(position).forEach { movePosition ->
                drawSquareHighlight(canvas, movePosition, possibleMovePaint)
            }
        }
        
        // Highlight king if in check
        if (chessGame.gameState == ChessGame.GameState.CHECK) {
            findKingPosition(chessGame.currentPlayer)?.let { kingPosition ->
                drawSquareHighlight(canvas, kingPosition, checkSquarePaint)
            }
        }
    }
    
    private fun drawSquareHighlight(canvas: Canvas, position: Position, paint: Paint) {
        val left = offsetX + position.col * squareSize
        val top = offsetY + position.row * squareSize
        val right = left + squareSize
        val bottom = top + squareSize
        
        canvas.drawRect(left, top, right, bottom, paint)
    }
    
    private fun drawPieces(canvas: Canvas) {
        for (row in 0..7) {
            for (col in 0..7) {
                val piece = chessGame.getPiece(Position(row, col))
                piece?.let {
                    val centerX = offsetX + col * squareSize + squareSize / 2f
                    val centerY = offsetY + row * squareSize + squareSize / 2f
                    
                    // Adjust text position for better centering
                    val textY = centerY + textPaint.textSize / 3f
                    
                    canvas.drawText(it.getSymbol(), centerX, textY, textPaint)
                }
            }
        }
    }
    
    private fun drawBorder(canvas: Canvas) {
        canvas.drawRect(offsetX, offsetY, offsetX + boardSize, offsetY + boardSize, borderPaint)
    }
    
    private fun findKingPosition(color: PieceColor): Position? {
        for (row in 0..7) {
            for (col in 0..7) {
                val piece = chessGame.getPiece(Position(row, col))
                if (piece?.type == PieceType.KING && piece.color == color) {
                    return Position(row, col)
                }
            }
        }
        return null
    }
    
    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (event.action == MotionEvent.ACTION_DOWN) {
            val touchX = event.x
            val touchY = event.y
            
            // Check if touch is within the board
            if (touchX >= offsetX && touchX <= offsetX + boardSize &&
                touchY >= offsetY && touchY <= offsetY + boardSize) {
                
                val col = ((touchX - offsetX) / squareSize).toInt()
                val row = ((touchY - offsetY) / squareSize).toInt()
                
                if (row in 0..7 && col in 0..7) {
                    val position = Position(row, col)
                    val previousGameState = chessGame.gameState
                    if (chessGame.selectSquare(position)) {
                        // Check if a move was actually made (game state might have changed)
                        if (chessGame.gameState != previousGameState || chessGame.getSelectedPosition() == null) {
                            onMoveCompleted?.invoke()
                        }
                        invalidate() // Redraw the board
                        onGameStateChanged?.invoke(chessGame.getGameStatusText())
                    }
                }
            }
            return true
        }
        return super.onTouchEvent(event)
    }
    
    fun resetGame() {
        chessGame.resetGame()
        invalidate()
        onGameStateChanged?.invoke(chessGame.getGameStatusText())
    }
    
    fun newGame() {
        resetGame()
    }
    
    fun getGameStatusText(): String {
        return chessGame.getGameStatusText()
    }

    fun undoLastMove(): Boolean {
        val success = chessGame.undoLastMove()
        if (success) {
            invalidate()
            onGameStateChanged?.invoke(chessGame.getGameStatusText())
        }
        return success
    }

    fun canUndo(): Boolean {
        return chessGame.canUndo()
    }
}
