package com.example.chessgame.repository

import androidx.lifecycle.LiveData
import com.example.chessgame.database.*

class ChessRepository(
    private val playerDao: PlayerDao,
    private val gameRecordDao: GameRecordDao
) {
    
    // Player operations
    fun getAllPlayers(): LiveData<List<Player>> = playerDao.getAllPlayers()
    
    fun getPlayersByRating(): LiveData<List<Player>> = playerDao.getPlayersByRating()
    
    suspend fun getPlayerById(playerId: Long): Player? = playerDao.getPlayerById(playerId)
    
    suspend fun getPlayerByName(name: String): Player? = playerDao.getPlayerByName(name)
    
    suspend fun getPlayerByEmail(email: String): Player? = playerDao.getPlayerByEmail(email)
    
    suspend fun insertPlayer(player: Player): Long = playerDao.insertPlayer(player)
    
    suspend fun updatePlayer(player: Player) = playerDao.updatePlayer(player)
    
    suspend fun deletePlayer(player: Player) = playerDao.deletePlayer(player)
    
    suspend fun deletePlayerById(playerId: Long) = playerDao.deletePlayerById(playerId)
    
    suspend fun getPlayerCount(): Int = playerDao.getPlayerCount()
    
    fun searchPlayers(searchQuery: String): LiveData<List<Player>> = playerDao.searchPlayers(searchQuery)
    
    suspend fun updatePlayerStats(
        playerId: Long,
        won: Boolean,
        lost: Boolean,
        draw: Boolean,
        newRating: Int,
        gameDuration: Long
    ) = playerDao.updatePlayerStats(playerId, won, lost, draw, newRating, gameDuration)
    
    fun getTopPlayersByWins(limit: Int): LiveData<List<Player>> = playerDao.getTopPlayersByWins(limit)
    
    fun getTopPlayersByWinRate(limit: Int): LiveData<List<Player>> = playerDao.getTopPlayersByWinRate(limit)
    
    // Game record operations
    fun getAllGameRecords(): LiveData<List<GameRecord>> = gameRecordDao.getAllGameRecords()
    
    suspend fun getGameRecordById(gameId: Long): GameRecord? = gameRecordDao.getGameRecordById(gameId)
    
    fun getGameRecordsByPlayer(playerId: Long): LiveData<List<GameRecord>> = 
        gameRecordDao.getGameRecordsByPlayer(playerId)
    
    fun getGameRecordsBetweenPlayers(player1Id: Long, player2Id: Long): LiveData<List<GameRecord>> = 
        gameRecordDao.getGameRecordsBetweenPlayers(player1Id, player2Id)
    
    fun getGameRecordsByResult(result: GameResult): LiveData<List<GameRecord>> = 
        gameRecordDao.getGameRecordsByResult(result)
    
    fun getGameRecordsByMode(mode: GameMode): LiveData<List<GameRecord>> = 
        gameRecordDao.getGameRecordsByMode(mode)
    
    suspend fun insertGameRecord(gameRecord: GameRecord): Long = gameRecordDao.insertGameRecord(gameRecord)
    
    suspend fun updateGameRecord(gameRecord: GameRecord) = gameRecordDao.updateGameRecord(gameRecord)
    
    suspend fun deleteGameRecord(gameRecord: GameRecord) = gameRecordDao.deleteGameRecord(gameRecord)
    
    suspend fun deleteGameRecordById(gameId: Long) = gameRecordDao.deleteGameRecordById(gameId)
    
    suspend fun getGameRecordCount(): Int = gameRecordDao.getGameRecordCount()
    
    suspend fun getGameCountForPlayer(playerId: Long): Int = gameRecordDao.getGameCountForPlayer(playerId)
    
    suspend fun getWinCountForPlayer(playerId: Long): Int = gameRecordDao.getWinCountForPlayer(playerId)
    
    suspend fun getLossCountForPlayer(playerId: Long): Int = gameRecordDao.getLossCountForPlayer(playerId)
    
    suspend fun getDrawCountForPlayer(playerId: Long): Int = gameRecordDao.getDrawCountForPlayer(playerId)
    
    suspend fun getAverageGameDurationForPlayer(playerId: Long): Long? = 
        gameRecordDao.getAverageGameDurationForPlayer(playerId)
    
    fun getRecentGameRecords(limit: Int): LiveData<List<GameRecord>> = 
        gameRecordDao.getRecentGameRecords(limit)
    
    fun getActiveGameRecords(): LiveData<List<GameRecord>> = gameRecordDao.getActiveGameRecords()
    
    // Combined operations
    suspend fun createPlayerProfile(name: String, email: String? = null): Long {
        val player = Player(
            name = name,
            email = email,
            createdAt = java.util.Date()
        )
        return insertPlayer(player)
    }
    
    suspend fun recordGameResult(
        whitePlayerId: Long,
        blackPlayerId: Long?,
        result: GameResult,
        mode: GameMode,
        startTime: java.util.Date,
        endTime: java.util.Date,
        totalMoves: Int,
        moveHistory: String = "",
        finalPosition: String = ""
    ): Long {
        val gameDuration = endTime.time - startTime.time
        
        val gameRecord = GameRecord(
            whitePlayerId = whitePlayerId,
            blackPlayerId = blackPlayerId,
            gameResult = result,
            gameMode = mode,
            startTime = startTime,
            endTime = endTime,
            totalMoves = totalMoves,
            gameDuration = gameDuration,
            moveHistory = moveHistory,
            finalPosition = finalPosition
        )
        
        val gameId = insertGameRecord(gameRecord)
        
        // Update player statistics
        updatePlayerStatsAfterGame(whitePlayerId, blackPlayerId, result, gameDuration)
        
        return gameId
    }
    
    private suspend fun updatePlayerStatsAfterGame(
        whitePlayerId: Long,
        blackPlayerId: Long?,
        result: GameResult,
        gameDuration: Long
    ) {
        // Update white player stats
        val whiteWon = result == GameResult.WHITE_WINS
        val whiteLost = result == GameResult.BLACK_WINS
        val whiteDraw = result == GameResult.DRAW || result == GameResult.STALEMATE
        
        val whitePlayer = getPlayerById(whitePlayerId)
        whitePlayer?.let { player ->
            val newRating = calculateNewRating(player.rating, whiteWon, whiteLost, whiteDraw)
            updatePlayerStats(whitePlayerId, whiteWon, whiteLost, whiteDraw, newRating, gameDuration)
        }
        
        // Update black player stats (if not AI)
        blackPlayerId?.let { blackId ->
            val blackWon = result == GameResult.BLACK_WINS
            val blackLost = result == GameResult.WHITE_WINS
            val blackDraw = result == GameResult.DRAW || result == GameResult.STALEMATE
            
            val blackPlayer = getPlayerById(blackId)
            blackPlayer?.let { player ->
                val newRating = calculateNewRating(player.rating, blackWon, blackLost, blackDraw)
                updatePlayerStats(blackId, blackWon, blackLost, blackDraw, newRating, gameDuration)
            }
        }
    }
    
    private fun calculateNewRating(currentRating: Int, won: Boolean, lost: Boolean, draw: Boolean): Int {
        // Simple ELO-like rating calculation
        return when {
            won -> currentRating + 20
            lost -> maxOf(800, currentRating - 20) // Minimum rating of 800
            draw -> currentRating + 5
            else -> currentRating
        }
    }
}
