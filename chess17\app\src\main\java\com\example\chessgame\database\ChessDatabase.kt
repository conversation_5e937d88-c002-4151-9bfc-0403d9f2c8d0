package com.example.chessgame.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.sqlite.db.SupportSQLiteDatabase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.Date

@Database(
    entities = [Player::class, GameRecord::class],
    version = 1,
    exportSchema = false
)
@TypeConverters(Converters::class)
abstract class ChessDatabase : RoomDatabase() {
    
    abstract fun playerDao(): PlayerDao
    abstract fun gameRecordDao(): GameRecordDao
    
    companion object {
        @Volatile
        private var INSTANCE: ChessDatabase? = null
        
        fun getDatabase(context: Context): ChessDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    ChessDatabase::class.java,
                    "chess_database"
                )
                .addCallback(DatabaseCallback())
                .build()
                INSTANCE = instance
                instance
            }
        }
    }
    
    private class DatabaseCallback : RoomDatabase.Callback() {
        override fun onCreate(db: SupportSQLiteDatabase) {
            super.onCreate(db)
            INSTANCE?.let { database ->
                CoroutineScope(Dispatchers.IO).launch {
                    populateDatabase(database.playerDao())
                }
            }
        }
        
        suspend fun populateDatabase(playerDao: PlayerDao) {
            // Add some default players for demonstration
            val defaultPlayers = listOf(
                Player(
                    name = "Guest Player",
                    email = null,
                    createdAt = Date(),
                    rating = 1200
                ),
                Player(
                    name = "Computer",
                    email = null,
                    createdAt = Date(),
                    rating = 1500,
                    favoriteColor = "BLACK"
                )
            )
            
            defaultPlayers.forEach { player ->
                playerDao.insertPlayer(player)
            }
        }
    }
}
