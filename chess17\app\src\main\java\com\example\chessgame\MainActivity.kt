package com.example.chessgame

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AlertDialog
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.example.chessgame.database.GameMode
import com.example.chessgame.database.GameResult
import com.example.chessgame.database.Player
import com.example.chessgame.ui.ProfileActivity
import com.example.chessgame.viewmodel.ProfileViewModel
import kotlinx.coroutines.launch
import java.util.Date

class MainActivity : AppCompatActivity() {

    private lateinit var chessBoardView: ChessBoardView
    private lateinit var tvGameStatus: TextView
    private lateinit var tvCurrentPlayer: TextView
    private lateinit var btnNewGame: Button
    private lateinit var btnUndo: Button
    private lateinit var btnReset: Button

    private lateinit var profileViewModel: ProfileViewModel
    private var currentPlayer: Player? = null
    private var gameStartTime: Date? = null
    private var totalMoves = 0
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        setupViewModel()
        initializeViews()
        setupEventListeners()
        handleIntent()
        updateGameStatus()
    }
    
    private fun setupViewModel() {
        profileViewModel = ViewModelProvider(this)[ProfileViewModel::class.java]
    }

    private fun initializeViews() {
        chessBoardView = findViewById(R.id.chessBoardView)
        tvGameStatus = findViewById(R.id.tvGameStatus)
        tvCurrentPlayer = findViewById(R.id.tvCurrentPlayer)
        btnNewGame = findViewById(R.id.btnNewGame)
        btnUndo = findViewById(R.id.btnUndo)
        btnReset = findViewById(R.id.btnReset)
    }

    private fun handleIntent() {
        val playerId = intent.getLongExtra("SELECTED_PLAYER_ID", -1L)
        val playerName = intent.getStringExtra("PLAYER_NAME")

        if (playerId != -1L && playerName != null) {
            lifecycleScope.launch {
                currentPlayer = profileViewModel.getPlayerById(playerId)
                updatePlayerDisplay()
                startNewGame()
            }
        } else {
            // No player selected, show profile selection
            showProfileSelection()
        }
    }

    private fun updatePlayerDisplay() {
        currentPlayer?.let { player ->
            tvCurrentPlayer.text = "Playing as: ${player.name} (Rating: ${player.rating})"
        } ?: run {
            tvCurrentPlayer.text = "No player selected"
        }
    }

    private fun showProfileSelection() {
        val intent = Intent(this, ProfileActivity::class.java)
        startActivity(intent)
        finish()
    }
    
    private fun setupEventListeners() {
        // Set up chess board callback
        chessBoardView.onGameStateChanged = { statusText ->
            updateGameStatus(statusText)
            updateButtonStates()
            checkGameEnd(statusText)
        }

        chessBoardView.onMoveCompleted = {
            totalMoves++
        }

        // New Game button
        btnNewGame.setOnClickListener {
            if (currentPlayer != null) {
                showNewGameDialog()
            } else {
                showProfileSelection()
            }
        }

        // Undo button
        btnUndo.setOnClickListener {
            chessBoardView.undoLastMove()
            updateGameStatus()
            updateButtonStates()
        }

        // Reset button
        btnReset.setOnClickListener {
            showResetDialog()
        }
    }
    
    private fun updateGameStatus(statusText: String? = null) {
        val status = statusText ?: chessBoardView.getGameStatusText()
        tvGameStatus.text = status

        // Update status text color based on game state
        when {
            status.contains("Check") -> {
                tvGameStatus.setTextColor(getColor(R.color.error))
            }
            status.contains("Wins") || status.contains("Checkmate") -> {
                tvGameStatus.setTextColor(getColor(R.color.primary))
            }
            status.contains("Stalemate") || status.contains("Draw") -> {
                tvGameStatus.setTextColor(getColor(R.color.secondary))
            }
            else -> {
                tvGameStatus.setTextColor(getColor(R.color.on_background))
            }
        }
    }

    private fun updateButtonStates() {
        btnUndo.isEnabled = chessBoardView.canUndo()
    }
    
    private fun checkGameEnd(statusText: String) {
        when {
            statusText.contains("Wins") || statusText.contains("Checkmate") -> {
                recordGameResult(statusText)
                showGameEndDialog(statusText, "Game Over")
            }
            statusText.contains("Stalemate") -> {
                recordGameResult("Stalemate")
                showGameEndDialog("The game ended in a stalemate!", "Stalemate")
            }
            statusText.contains("Draw") -> {
                recordGameResult("Draw")
                showGameEndDialog("The game ended in a draw!", "Draw")
            }
        }
    }

    private fun recordGameResult(statusText: String) {
        currentPlayer?.let { player ->
            gameStartTime?.let { startTime ->
                val endTime = Date()
                val result = when {
                    statusText.contains("White Wins") -> GameResult.WHITE_WINS
                    statusText.contains("Black Wins") -> GameResult.BLACK_WINS
                    statusText.contains("Stalemate") -> GameResult.STALEMATE
                    statusText.contains("Draw") -> GameResult.DRAW
                    else -> GameResult.DRAW
                }

                profileViewModel.recordGameResult(
                    whitePlayerId = player.id,
                    blackPlayerId = null, // Single player mode
                    result = result,
                    mode = GameMode.SINGLE_PLAYER,
                    startTime = startTime,
                    endTime = endTime,
                    totalMoves = totalMoves
                )
            }
        }
    }

    private fun startNewGame() {
        gameStartTime = Date()
        totalMoves = 0
    }
    
    private fun showGameEndDialog(message: String, title: String) {
        AlertDialog.Builder(this)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton("New Game") { _, _ ->
                chessBoardView.newGame()
                startNewGame()
                updateGameStatus()
                updateButtonStates()
            }
            .setNegativeButton("Continue Viewing") { dialog, _ ->
                dialog.dismiss()
            }
            .setCancelable(false)
            .show()
    }
    
    private fun showNewGameDialog() {
        AlertDialog.Builder(this)
            .setTitle("New Game")
            .setMessage("Are you sure you want to start a new game? Current progress will be lost.")
            .setPositiveButton("Yes") { _, _ ->
                chessBoardView.newGame()
                startNewGame()
                updateGameStatus()
                updateButtonStates()
            }
            .setNegativeButton("Cancel") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
    
    private fun showResetDialog() {
        AlertDialog.Builder(this)
            .setTitle("Reset Game")
            .setMessage("Are you sure you want to reset the current game?")
            .setPositiveButton("Yes") { _, _ ->
                chessBoardView.resetGame()
                startNewGame()
                updateGameStatus()
                updateButtonStates()
            }
            .setNegativeButton("Cancel") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
    
    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.main_menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_profiles -> {
                val intent = Intent(this, ProfileActivity::class.java)
                startActivity(intent)
                true
            }
            R.id.action_switch_player -> {
                showProfileSelection()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onResume() {
        super.onResume()
        updateGameStatus()
        updateButtonStates()
        updatePlayerDisplay()
    }
}
