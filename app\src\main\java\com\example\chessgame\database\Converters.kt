package com.example.chessgame.database

import androidx.room.TypeConverter
import java.util.Date

class Converters {
    @TypeConverter
    fun fromTimestamp(value: Long?): Date? {
        return value?.let { Date(it) }
    }

    @TypeConverter
    fun dateToTimestamp(date: Date?): Long? {
        return date?.time
    }

    @TypeConverter
    fun fromGameResult(value: GameResult): String {
        return value.name
    }

    @TypeConverter
    fun toGameResult(value: String): GameResult {
        return GameResult.valueOf(value)
    }

    @TypeConverter
    fun fromGameMode(value: GameMode): String {
        return value.name
    }

    @TypeConverter
    fun toGameMode(value: String): GameMode {
        return GameMode.valueOf(value)
    }
}
