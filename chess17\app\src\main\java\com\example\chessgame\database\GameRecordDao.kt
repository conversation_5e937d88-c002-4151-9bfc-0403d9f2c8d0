package com.example.chessgame.database

import androidx.lifecycle.LiveData
import androidx.room.*

@Dao
interface GameRecordDao {
    
    @Query("SELECT * FROM game_records ORDER BY startTime DESC")
    fun getAllGameRecords(): LiveData<List<GameRecord>>
    
    @Query("SELECT * FROM game_records WHERE id = :gameId")
    suspend fun getGameRecordById(gameId: Long): GameRecord?
    
    @Query("""
        SELECT * FROM game_records 
        WHERE whitePlayerId = :playerId OR blackPlayerId = :playerId 
        ORDER BY startTime DESC
    """)
    fun getGameRecordsByPlayer(playerId: Long): LiveData<List<GameRecord>>
    
    @Query("""
        SELECT * FROM game_records 
        WHERE (whitePlayerId = :player1Id AND blackPlayerId = :player2Id) 
           OR (whitePlayerId = :player2Id AND blackPlayerId = :player1Id)
        ORDER BY startTime DESC
    """)
    fun getGameRecordsBetweenPlayers(player1Id: Long, player2Id: Long): LiveData<List<GameRecord>>
    
    @Query("SELECT * FROM game_records WHERE gameResult = :result ORDER BY startTime DESC")
    fun getGameRecordsByResult(result: GameResult): LiveData<List<GameRecord>>
    
    @Query("SELECT * FROM game_records WHERE gameMode = :mode ORDER BY startTime DESC")
    fun getGameRecordsByMode(mode: GameMode): LiveData<List<GameRecord>>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertGameRecord(gameRecord: GameRecord): Long
    
    @Update
    suspend fun updateGameRecord(gameRecord: GameRecord)
    
    @Delete
    suspend fun deleteGameRecord(gameRecord: GameRecord)
    
    @Query("DELETE FROM game_records WHERE id = :gameId")
    suspend fun deleteGameRecordById(gameId: Long)
    
    @Query("SELECT COUNT(*) FROM game_records")
    suspend fun getGameRecordCount(): Int
    
    @Query("SELECT COUNT(*) FROM game_records WHERE whitePlayerId = :playerId OR blackPlayerId = :playerId")
    suspend fun getGameCountForPlayer(playerId: Long): Int
    
    @Query("""
        SELECT COUNT(*) FROM game_records 
        WHERE (whitePlayerId = :playerId AND gameResult = 'WHITE_WINS') 
           OR (blackPlayerId = :playerId AND gameResult = 'BLACK_WINS')
    """)
    suspend fun getWinCountForPlayer(playerId: Long): Int
    
    @Query("""
        SELECT COUNT(*) FROM game_records 
        WHERE (whitePlayerId = :playerId AND gameResult = 'BLACK_WINS') 
           OR (blackPlayerId = :playerId AND gameResult = 'WHITE_WINS')
    """)
    suspend fun getLossCountForPlayer(playerId: Long): Int
    
    @Query("""
        SELECT COUNT(*) FROM game_records 
        WHERE (whitePlayerId = :playerId OR blackPlayerId = :playerId) 
           AND gameResult IN ('DRAW', 'STALEMATE')
    """)
    suspend fun getDrawCountForPlayer(playerId: Long): Int
    
    @Query("SELECT AVG(gameDuration) FROM game_records WHERE whitePlayerId = :playerId OR blackPlayerId = :playerId")
    suspend fun getAverageGameDurationForPlayer(playerId: Long): Long?
    
    @Query("SELECT * FROM game_records ORDER BY startTime DESC LIMIT :limit")
    fun getRecentGameRecords(limit: Int): LiveData<List<GameRecord>>
    
    @Query("SELECT * FROM game_records WHERE endTime IS NULL")
    fun getActiveGameRecords(): LiveData<List<GameRecord>>
}
