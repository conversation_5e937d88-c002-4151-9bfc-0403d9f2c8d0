package com.example.chessgame

import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AlertDialog

class MainActivity : AppCompatActivity() {
    
    private lateinit var chessBoardView: ChessBoardView
    private lateinit var tvGameStatus: TextView
    private lateinit var btnNewGame: Button
    private lateinit var btnUndo: Button
    private lateinit var btnReset: Button
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)
        
        initializeViews()
        setupEventListeners()
        updateGameStatus()
    }
    
    private fun initializeViews() {
        chessBoardView = findViewById(R.id.chessBoardView)
        tvGameStatus = findViewById(R.id.tvGameStatus)
        btnNewGame = findViewById(R.id.btnNewGame)
        btnUndo = findViewById(R.id.btnUndo)
        btnReset = findViewById(R.id.btnReset)
    }
    
    private fun setupEventListeners() {
        // Set up chess board callback
        chessBoardView.onGameStateChanged = { statusText ->
            updateGameStatus(statusText)
            updateButtonStates()
            checkGameEnd(statusText)
        }

        // New Game button
        btnNewGame.setOnClickListener {
            showNewGameDialog()
        }

        // Undo button
        btnUndo.setOnClickListener {
            chessBoardView.undoLastMove()
            updateGameStatus()
            updateButtonStates()
        }

        // Reset button
        btnReset.setOnClickListener {
            showResetDialog()
        }
    }
    
    private fun updateGameStatus(statusText: String? = null) {
        val status = statusText ?: chessBoardView.getGameStatusText()
        tvGameStatus.text = status

        // Update status text color based on game state
        when {
            status.contains("Check") -> {
                tvGameStatus.setTextColor(getColor(R.color.error))
            }
            status.contains("Wins") || status.contains("Checkmate") -> {
                tvGameStatus.setTextColor(getColor(R.color.primary))
            }
            status.contains("Stalemate") || status.contains("Draw") -> {
                tvGameStatus.setTextColor(getColor(R.color.secondary))
            }
            else -> {
                tvGameStatus.setTextColor(getColor(R.color.on_background))
            }
        }
    }

    private fun updateButtonStates() {
        btnUndo.isEnabled = chessBoardView.canUndo()
    }
    
    private fun checkGameEnd(statusText: String) {
        when {
            statusText.contains("Wins") || statusText.contains("Checkmate") -> {
                showGameEndDialog(statusText, "Game Over")
            }
            statusText.contains("Stalemate") -> {
                showGameEndDialog("The game ended in a stalemate!", "Stalemate")
            }
            statusText.contains("Draw") -> {
                showGameEndDialog("The game ended in a draw!", "Draw")
            }
        }
    }
    
    private fun showGameEndDialog(message: String, title: String) {
        AlertDialog.Builder(this)
            .setTitle(title)
            .setMessage(message)
            .setPositiveButton("New Game") { _, _ ->
                chessBoardView.newGame()
                updateGameStatus()
                updateButtonStates()
            }
            .setNegativeButton("Continue Viewing") { dialog, _ ->
                dialog.dismiss()
            }
            .setCancelable(false)
            .show()
    }
    
    private fun showNewGameDialog() {
        AlertDialog.Builder(this)
            .setTitle("New Game")
            .setMessage("Are you sure you want to start a new game? Current progress will be lost.")
            .setPositiveButton("Yes") { _, _ ->
                chessBoardView.newGame()
                updateGameStatus()
                updateButtonStates()
            }
            .setNegativeButton("Cancel") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
    
    private fun showResetDialog() {
        AlertDialog.Builder(this)
            .setTitle("Reset Game")
            .setMessage("Are you sure you want to reset the current game?")
            .setPositiveButton("Yes") { _, _ ->
                chessBoardView.resetGame()
                updateGameStatus()
                updateButtonStates()
            }
            .setNegativeButton("Cancel") { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }
    
    override fun onResume() {
        super.onResume()
        updateGameStatus()
        updateButtonStates()
    }
}
