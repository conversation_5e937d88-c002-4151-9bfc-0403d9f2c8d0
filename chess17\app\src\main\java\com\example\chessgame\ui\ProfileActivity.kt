package com.example.chessgame.ui

import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.chessgame.MainActivity
import com.example.chessgame.R
import com.example.chessgame.database.Player
import com.example.chessgame.viewmodel.ProfileViewModel
import com.google.android.material.floatingactionbutton.FloatingActionButton

class ProfileActivity : AppCompatActivity() {
    
    private lateinit var profileViewModel: ProfileViewModel
    private lateinit var profileAdapter: ProfileAdapter
    private lateinit var recyclerView: RecyclerView
    private lateinit var fabAddProfile: FloatingActionButton
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_profile)
        
        setupViews()
        setupViewModel()
        setupRecyclerView()
        observeData()
    }
    
    private fun setupViews() {
        recyclerView = findViewById(R.id.recyclerViewProfiles)
        fabAddProfile = findViewById(R.id.fabAddProfile)
        
        supportActionBar?.title = "Player Profiles"
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        
        fabAddProfile.setOnClickListener {
            val intent = Intent(this, CreateProfileActivity::class.java)
            startActivity(intent)
        }
    }
    
    private fun setupViewModel() {
        profileViewModel = ViewModelProvider(this)[ProfileViewModel::class.java]
    }
    
    private fun setupRecyclerView() {
        profileAdapter = ProfileAdapter(
            onPlayerClick = { player ->
                val intent = Intent(this, PlayerDetailActivity::class.java)
                intent.putExtra("PLAYER_ID", player.id)
                startActivity(intent)
            },
            onPlayAsClick = { player ->
                val intent = Intent(this, MainActivity::class.java)
                intent.putExtra("SELECTED_PLAYER_ID", player.id)
                intent.putExtra("PLAYER_NAME", player.name)
                startActivity(intent)
                finish()
            },
            onDeleteClick = { player ->
                showDeleteConfirmation(player)
            }
        )
        
        recyclerView.apply {
            layoutManager = LinearLayoutManager(this@ProfileActivity)
            adapter = profileAdapter
        }
    }
    
    private fun observeData() {
        profileViewModel.allPlayers.observe(this) { players ->
            profileAdapter.submitList(players)
        }
    }
    
    private fun showDeleteConfirmation(player: Player) {
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("Delete Profile")
            .setMessage("Are you sure you want to delete ${player.name}'s profile? This will also delete all game history.")
            .setPositiveButton("Delete") { _, _ ->
                profileViewModel.deletePlayer(player)
                Toast.makeText(this, "${player.name}'s profile deleted", Toast.LENGTH_SHORT).show()
            }
            .setNegativeButton("Cancel", null)
            .show()
    }
    
    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
