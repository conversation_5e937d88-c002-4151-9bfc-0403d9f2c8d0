package com.example.chessgame.ui

import android.os.Bundle
import android.widget.Button
import android.widget.EditText
import android.widget.RadioGroup
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.example.chessgame.R
import com.example.chessgame.database.Player
import com.example.chessgame.viewmodel.ProfileViewModel
import kotlinx.coroutines.launch
import java.util.Date

class CreateProfileActivity : AppCompatActivity() {
    
    private lateinit var profileViewModel: ProfileViewModel
    private lateinit var etPlayerName: EditText
    private lateinit var etPlayerEmail: EditText
    private lateinit var rgFavoriteColor: RadioGroup
    private lateinit var btnCreateProfile: Button
    private lateinit var btnCancel: Button
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_create_profile)
        
        setupViews()
        setupViewModel()
        setupClickListeners()
    }
    
    private fun setupViews() {
        etPlayerName = findViewById(R.id.etPlayerName)
        etPlayerEmail = findViewById(R.id.etPlayerEmail)
        rgFavoriteColor = findViewById(R.id.rgFavoriteColor)
        btnCreateProfile = findViewById(R.id.btnCreateProfile)
        btnCancel = findViewById(R.id.btnCancel)
        
        supportActionBar?.title = "Create New Profile"
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
    }
    
    private fun setupViewModel() {
        profileViewModel = ViewModelProvider(this)[ProfileViewModel::class.java]
    }
    
    private fun setupClickListeners() {
        btnCreateProfile.setOnClickListener {
            createProfile()
        }
        
        btnCancel.setOnClickListener {
            finish()
        }
    }
    
    private fun createProfile() {
        val name = etPlayerName.text.toString().trim()
        val email = etPlayerEmail.text.toString().trim().takeIf { it.isNotEmpty() }
        
        if (name.isEmpty()) {
            etPlayerName.error = "Player name is required"
            etPlayerName.requestFocus()
            return
        }
        
        if (name.length < 2) {
            etPlayerName.error = "Player name must be at least 2 characters"
            etPlayerName.requestFocus()
            return
        }
        
        // Validate email if provided
        if (email != null && !isValidEmail(email)) {
            etPlayerEmail.error = "Please enter a valid email address"
            etPlayerEmail.requestFocus()
            return
        }
        
        val favoriteColor = when (rgFavoriteColor.checkedRadioButtonId) {
            R.id.rbWhite -> "WHITE"
            R.id.rbBlack -> "BLACK"
            else -> "WHITE"
        }
        
        lifecycleScope.launch {
            try {
                // Check if player name already exists
                val existingPlayer = profileViewModel.getPlayerByName(name)
                if (existingPlayer != null) {
                    etPlayerName.error = "A player with this name already exists"
                    etPlayerName.requestFocus()
                    return@launch
                }
                
                // Check if email already exists (if provided)
                if (email != null) {
                    val existingEmailPlayer = profileViewModel.getPlayerByEmail(email)
                    if (existingEmailPlayer != null) {
                        etPlayerEmail.error = "A player with this email already exists"
                        etPlayerEmail.requestFocus()
                        return@launch
                    }
                }
                
                val newPlayer = Player(
                    name = name,
                    email = email,
                    favoriteColor = favoriteColor,
                    createdAt = Date()
                )
                
                profileViewModel.insertPlayer(newPlayer)
                
                Toast.makeText(this@CreateProfileActivity, "Profile created successfully!", Toast.LENGTH_SHORT).show()
                finish()
                
            } catch (e: Exception) {
                Toast.makeText(this@CreateProfileActivity, "Error creating profile: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }
    
    private fun isValidEmail(email: String): Boolean {
        return android.util.Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }
    
    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
