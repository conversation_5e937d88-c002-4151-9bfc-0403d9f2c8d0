<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Player Avatar Placeholder -->
        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:layout_gravity="center"
            android:src="@android:drawable/ic_menu_myplaces"
            android:background="@color/light_square"
            android:padding="24dp"
            android:layout_marginBottom="24dp" />

        <!-- Player Name -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Player Name"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/on_background"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/tvPlayerName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="John Doe"
            android:textSize="20sp"
            android:textColor="@color/primary"
            android:layout_marginBottom="16dp" />

        <!-- Email -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Email"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/on_background"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/tvPlayerEmail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="<EMAIL>"
            android:textSize="16sp"
            android:textColor="@color/on_surface"
            android:layout_marginBottom="16dp" />

        <!-- Statistics Card -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Statistics"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Rating:"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvPlayerRating"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="1200"
                        android:textColor="@color/secondary" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Games Played:"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvGamesPlayed"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="8dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Win Rate:"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvWinRate"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="0.0%" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="Favorite Color:"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvFavoriteColor"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="White" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- Profile Info -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Member Since"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/on_background"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/tvCreatedDate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="January 1, 2024"
            android:textSize="16sp"
            android:textColor="@color/on_surface"
            android:layout_marginBottom="24dp" />

        <!-- Play Button -->
        <Button
            android:id="@+id/btnPlayAsPlayer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Play as this player"
            android:textSize="16sp"
            style="@style/Widget.MaterialComponents.Button" />

    </LinearLayout>

</ScrollView>
