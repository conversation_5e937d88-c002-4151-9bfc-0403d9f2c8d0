<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvPlayerName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Player Name"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:textColor="@color/primary" />

                <TextView
                    android:id="@+id/tvPlayerEmail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="<EMAIL>"
                    android:textSize="14sp"
                    android:textColor="@color/on_surface"
                    android:layout_marginTop="2dp" />

            </LinearLayout>

            <ImageButton
                android:id="@+id/btnDelete"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@android:drawable/ic_menu_delete"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="Delete profile"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="8dp">

            <TextView
                android:id="@+id/tvPlayerRating"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Rating: 1200"
                android:textSize="14sp"
                android:textStyle="bold"
                android:textColor="@color/secondary" />

        </LinearLayout>

        <TextView
            android:id="@+id/tvPlayerStats"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Games: 0 | Wins: 0 | Losses: 0 | Draws: 0"
            android:textSize="12sp"
            android:textColor="@color/on_surface"
            android:layout_marginTop="4dp" />

        <TextView
            android:id="@+id/tvCreatedDate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Created: Jan 01, 2024"
            android:textSize="12sp"
            android:textColor="@color/on_surface"
            android:layout_marginTop="2dp" />

        <Button
            android:id="@+id/btnPlayAs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Play as this player"
            android:layout_marginTop="12dp"
            style="@style/Widget.MaterialComponents.Button" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
