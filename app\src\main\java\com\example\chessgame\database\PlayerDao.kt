package com.example.chessgame.database

import androidx.lifecycle.LiveData
import androidx.room.*

@Dao
interface PlayerDao {
    
    @Query("SELECT * FROM players ORDER BY name ASC")
    fun getAllPlayers(): LiveData<List<Player>>
    
    @Query("SELECT * FROM players ORDER BY rating DESC")
    fun getPlayersByRating(): LiveData<List<Player>>
    
    @Query("SELECT * FROM players WHERE id = :playerId")
    suspend fun getPlayerById(playerId: Long): Player?
    
    @Query("SELECT * FROM players WHERE name = :name LIMIT 1")
    suspend fun getPlayerByName(name: String): Player?
    
    @Query("SELECT * FROM players WHERE email = :email LIMIT 1")
    suspend fun getPlayerByEmail(email: String): Player?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPlayer(player: Player): Long
    
    @Update
    suspend fun updatePlayer(player: Player)
    
    @Delete
    suspend fun deletePlayer(player: Player)
    
    @Query("DELETE FROM players WHERE id = :playerId")
    suspend fun deletePlayerById(playerId: Long)
    
    @Query("SELECT COUNT(*) FROM players")
    suspend fun getPlayerCount(): Int
    
    @Query("SELECT * FROM players WHERE name LIKE '%' || :searchQuery || '%' OR email LIKE '%' || :searchQuery || '%'")
    fun searchPlayers(searchQuery: String): LiveData<List<Player>>
    
    // Update player statistics
    @Query("""
        UPDATE players 
        SET gamesPlayed = gamesPlayed + 1,
            gamesWon = CASE WHEN :won = 1 THEN gamesWon + 1 ELSE gamesWon END,
            gamesLost = CASE WHEN :lost = 1 THEN gamesLost + 1 ELSE gamesLost END,
            gamesDraw = CASE WHEN :draw = 1 THEN gamesDraw + 1 ELSE gamesDraw END,
            rating = :newRating,
            totalPlayTime = totalPlayTime + :gameDuration
        WHERE id = :playerId
    """)
    suspend fun updatePlayerStats(
        playerId: Long,
        won: Boolean,
        lost: Boolean,
        draw: Boolean,
        newRating: Int,
        gameDuration: Long
    )
    
    @Query("SELECT * FROM players ORDER BY gamesWon DESC LIMIT :limit")
    fun getTopPlayersByWins(limit: Int): LiveData<List<Player>>
    
    @Query("SELECT * FROM players WHERE gamesPlayed > 0 ORDER BY (CAST(gamesWon AS REAL) / CAST(gamesPlayed AS REAL)) DESC LIMIT :limit")
    fun getTopPlayersByWinRate(limit: Int): LiveData<List<Player>>
}
