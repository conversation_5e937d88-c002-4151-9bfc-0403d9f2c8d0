package com.example.chessgame.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.LiveData
import androidx.lifecycle.viewModelScope
import com.example.chessgame.database.ChessDatabase
import com.example.chessgame.database.GameRecord
import com.example.chessgame.database.Player
import com.example.chessgame.repository.ChessRepository
import kotlinx.coroutines.launch

class ProfileViewModel(application: Application) : AndroidViewModel(application) {
    
    private val repository: ChessRepository
    
    val allPlayers: LiveData<List<Player>>
    val playersByRating: LiveData<List<Player>>
    
    init {
        val database = ChessDatabase.getDatabase(application)
        repository = ChessRepository(database.playerDao(), database.gameRecordDao())
        allPlayers = repository.getAllPlayers()
        playersByRating = repository.getPlayersByRating()
    }
    
    // Player operations
    fun insertPlayer(player: Player) = viewModelScope.launch {
        repository.insertPlayer(player)
    }
    
    fun updatePlayer(player: Player) = viewModelScope.launch {
        repository.updatePlayer(player)
    }
    
    fun deletePlayer(player: Player) = viewModelScope.launch {
        repository.deletePlayer(player)
    }
    
    suspend fun getPlayerById(playerId: Long): Player? {
        return repository.getPlayerById(playerId)
    }
    
    suspend fun getPlayerByName(name: String): Player? {
        return repository.getPlayerByName(name)
    }
    
    suspend fun getPlayerByEmail(email: String): Player? {
        return repository.getPlayerByEmail(email)
    }
    
    fun searchPlayers(searchQuery: String): LiveData<List<Player>> {
        return repository.searchPlayers(searchQuery)
    }
    
    fun getTopPlayersByWins(limit: Int): LiveData<List<Player>> {
        return repository.getTopPlayersByWins(limit)
    }
    
    fun getTopPlayersByWinRate(limit: Int): LiveData<List<Player>> {
        return repository.getTopPlayersByWinRate(limit)
    }
    
    // Game record operations
    fun getAllGameRecords(): LiveData<List<GameRecord>> {
        return repository.getAllGameRecords()
    }
    
    fun getGameRecordsByPlayer(playerId: Long): LiveData<List<GameRecord>> {
        return repository.getGameRecordsByPlayer(playerId)
    }
    
    fun getRecentGameRecords(limit: Int): LiveData<List<GameRecord>> {
        return repository.getRecentGameRecords(limit)
    }
    
    // Combined operations
    fun createPlayerProfile(name: String, email: String? = null) = viewModelScope.launch {
        repository.createPlayerProfile(name, email)
    }
    
    fun recordGameResult(
        whitePlayerId: Long,
        blackPlayerId: Long?,
        result: com.example.chessgame.database.GameResult,
        mode: com.example.chessgame.database.GameMode,
        startTime: java.util.Date,
        endTime: java.util.Date,
        totalMoves: Int,
        moveHistory: String = "",
        finalPosition: String = ""
    ) = viewModelScope.launch {
        repository.recordGameResult(
            whitePlayerId, blackPlayerId, result, mode, startTime, endTime,
            totalMoves, moveHistory, finalPosition
        )
    }
}
