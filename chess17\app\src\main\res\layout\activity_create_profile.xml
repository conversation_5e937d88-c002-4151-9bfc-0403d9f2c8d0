<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Create New Player Profile"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="32dp"
            android:textColor="@color/primary" />

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:boxStrokeColor="@color/primary"
            app:hintTextColor="@color/primary">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etPlayerName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Player Name *"
                android:inputType="textPersonName"
                android:maxLength="30" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            app:boxStrokeColor="@color/primary"
            app:hintTextColor="@color/primary">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/etPlayerEmail"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Email (Optional)"
                android:inputType="textEmailAddress" />

        </com.google.android.material.textfield.TextInputLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Favorite Color"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp"
            android:textColor="@color/on_background" />

        <RadioGroup
            android:id="@+id/rgFavoriteColor"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="32dp">

            <RadioButton
                android:id="@+id/rbWhite"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="White"
                android:checked="true" />

            <RadioButton
                android:id="@+id/rbBlack"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Black" />

        </RadioGroup>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="• Your profile will track game statistics\n• Starting rating: 1200\n• You can edit your profile later"
            android:textSize="14sp"
            android:textColor="@color/on_surface"
            android:layout_marginBottom="24dp"
            android:background="@color/light_square"
            android:padding="12dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <Button
                android:id="@+id/btnCancel"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Cancel"
                android:layout_marginEnd="8dp"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

            <Button
                android:id="@+id/btnCreateProfile"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Create Profile"
                android:layout_marginStart="8dp"
                style="@style/Widget.MaterialComponents.Button" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
