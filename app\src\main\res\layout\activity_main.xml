<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".MainActivity">

    <!-- Current Player -->
    <TextView
        android:id="@+id/tvCurrentPlayer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="No player selected"
        android:textSize="14sp"
        android:gravity="center"
        android:padding="4dp"
        android:textColor="@color/primary"
        android:layout_marginBottom="8dp" />

    <!-- Game Status -->
    <TextView
        android:id="@+id/tvGameStatus"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/white_turn"
        android:textSize="18sp"
        android:textStyle="bold"
        android:gravity="center"
        android:padding="8dp"
        android:background="@color/light_square"
        android:layout_marginBottom="16dp" />

    <!-- Chess Board Container -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center">

        <com.example.chessgame.ChessBoardView
            android:id="@+id/chessBoardView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center" />

    </FrameLayout>

    <!-- Control Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="16dp">

        <Button
            android:id="@+id/btnNewGame"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/new_game"
            android:layout_marginEnd="4dp" />

        <Button
            android:id="@+id/btnUndo"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Undo"
            android:layout_marginStart="4dp"
            android:layout_marginEnd="4dp" />

        <Button
            android:id="@+id/btnReset"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/reset_game"
            android:layout_marginStart="4dp" />

    </LinearLayout>

</LinearLayout>
