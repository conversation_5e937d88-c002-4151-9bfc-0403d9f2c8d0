# Chess17 - Android Chess Game with Player Profiles

A complete Android chess game with database-backed player profiles, statistics tracking, and modern Material Design UI.

## 🎮 Features

### Core Chess Game
- **Complete Chess Implementation**: All standard chess pieces with proper movement rules
- **Visual Chess Board**: Beautiful 8x8 chess board with alternating light and dark squares
- **Interactive Gameplay**: Touch to select pieces and make moves
- **Game State Management**: Tracks turns, check, checkmate, and stalemate conditions
- **Move Validation**: Ensures only legal moves are allowed
- **Visual Feedback**: 
  - Highlights selected pieces (yellow)
  - Shows possible moves (green)
  - Highlights king when in check (red)

### Player Profile System
- **Profile Management**: Create, view, edit, and delete player profiles
- **Statistics Tracking**: Games played, won, lost, drawn with win rate calculations
- **ELO Rating System**: Dynamic rating system that updates after each game (starts at 1200)
- **Game History**: Complete game records with timestamps and results
- **Profile Validation**: Email validation and duplicate prevention

### Database Features
- **Room Database**: Modern Android database with SQLite backend
- **Player Entity**: Stores comprehensive player information and statistics
- **Game Records**: Tracks individual game results, duration, and move counts
- **Repository Pattern**: Clean data access layer with coroutines support

## 📱 User Interface

### Profile Management
- **ProfileActivity**: Main launcher screen showing all player profiles
- **CreateProfileActivity**: Form to create new player profiles
- **PlayerDetailActivity**: Detailed view of player statistics and history
- **Material Design**: Beautiful cards, floating action buttons, and modern styling

### Game Interface
- **Current Player Display**: Shows selected player name and rating
- **Game Status**: Real-time game state updates
- **Control Buttons**: New Game, Undo, Reset with confirmation dialogs
- **Menu Options**: Profile management and player switching

## 🏗️ Project Structure

```
chess17/
├── app/
│   ├── build.gradle                 # App-level build configuration
│   ├── proguard-rules.pro          # ProGuard configuration
│   └── src/main/
│       ├── AndroidManifest.xml     # App manifest
│       ├── java/com/example/chessgame/
│       │   ├── MainActivity.kt      # Main game activity
│       │   ├── ChessGame.kt        # Core game logic
│       │   ├── ChessPiece.kt       # Piece definitions and movement
│       │   ├── ChessBoardView.kt   # Custom chess board view
│       │   ├── database/           # Database entities and DAOs
│       │   │   ├── Player.kt
│       │   │   ├── GameRecord.kt
│       │   │   ├── PlayerDao.kt
│       │   │   ├── GameRecordDao.kt
│       │   │   ├── ChessDatabase.kt
│       │   │   └── Converters.kt
│       │   ├── repository/         # Data repository
│       │   │   └── ChessRepository.kt
│       │   ├── ui/                 # UI activities and adapters
│       │   │   ├── ProfileActivity.kt
│       │   │   ├── CreateProfileActivity.kt
│       │   │   ├── PlayerDetailActivity.kt
│       │   │   └── ProfileAdapter.kt
│       │   └── viewmodel/          # ViewModels
│       │       └── ProfileViewModel.kt
│       └── res/
│           ├── layout/             # XML layouts
│           ├── values/             # Colors, strings, themes
│           ├── menu/               # Menu resources
│           └── xml/                # Data extraction rules
├── build.gradle                    # Project-level build configuration
├── settings.gradle                 # Project settings
└── README.md                       # This file
```

## 🚀 Getting Started

### Prerequisites
- Android Studio Arctic Fox or later
- Android SDK API level 24 (Android 7.0) or higher
- Kotlin support

### Installation
1. Open Android Studio
2. Click "Open an Existing Project"
3. Navigate to the `chess17` folder
4. Select the folder and click "OK"
5. Wait for Gradle sync to complete
6. Run the app on an emulator or physical device

### First Launch
1. The app opens with ProfileActivity showing available player profiles
2. Default profiles "Guest Player" and "Computer" are pre-created
3. Tap the "+" button to create your own profile
4. Fill in your name, email (optional), and favorite color
5. Tap "Play as this player" to start gaming

## 🎯 How to Play

### Game Controls
- **Piece Selection**: Tap any piece to select it (highlighted in yellow)
- **Move Pieces**: Tap destination square to move selected piece
- **Possible Moves**: Green highlights show valid moves
- **Undo**: Take back your last move
- **New Game**: Start a fresh game
- **Reset**: Reset current position

### Profile Features
- **Menu → Manage Profiles**: View all player profiles
- **Menu → Switch Player**: Change to different player
- **Profile Details**: Tap any profile to see detailed statistics

## 📊 Statistics Tracked

- Games played, won, lost, drawn
- Win rate percentage
- ELO-style rating (starts at 1200, adjusts ±20 per game)
- Game duration and total play time
- Move counts per game
- Game history with timestamps

## 🛠️ Technical Details

### Dependencies
- **Room Database**: 2.6.1 - Local database storage
- **Material Components**: 1.11.0 - Modern UI components
- **Lifecycle Components**: 2.7.0 - ViewModel and LiveData
- **Navigation Components**: 2.7.6 - Fragment navigation
- **RecyclerView**: 1.3.2 - List displays

### Architecture
- **MVVM Pattern**: ViewModel + LiveData + Repository
- **Room Database**: Type-safe database access
- **Coroutines**: Asynchronous database operations
- **Material Design**: Modern Android UI guidelines

## 🔮 Future Enhancements

Potential features for future versions:
- AI opponent with difficulty levels
- Online multiplayer support
- Chess notation (PGN) support
- Game analysis and move suggestions
- Tournament mode
- Export/import game data
- Sound effects and animations
- Castling and en passant moves
- Timer/clock functionality

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.
