@echo off
echo Copying all project files to chess17...

REM Create directory structure
mkdir chess17\app\src\main\java\com\example\chessgame\database 2>nul
mkdir chess17\app\src\main\java\com\example\chessgame\repository 2>nul
mkdir chess17\app\src\main\java\com\example\chessgame\ui 2>nul
mkdir chess17\app\src\main\java\com\example\chessgame\viewmodel 2>nul
mkdir chess17\app\src\main\res\layout 2>nul
mkdir chess17\app\src\main\res\menu 2>nul
mkdir chess17\app\src\main\res\xml 2>nul
mkdir chess17\app\src\main\res\values 2>nul

REM Copy all files
xcopy /Y app\src\main\java\com\example\chessgame\*.kt chess17\app\src\main\java\com\example\chessgame\
xcopy /Y app\src\main\java\com\example\chessgame\database\*.kt chess17\app\src\main\java\com\example\chessgame\database\
xcopy /Y app\src\main\java\com\example\chessgame\repository\*.kt chess17\app\src\main\java\com\example\chessgame\repository\
xcopy /Y app\src\main\java\com\example\chessgame\ui\*.kt chess17\app\src\main\java\com\example\chessgame\ui\
xcopy /Y app\src\main\java\com\example\chessgame\viewmodel\*.kt chess17\app\src\main\java\com\example\chessgame\viewmodel\
xcopy /Y app\src\main\res\layout\*.xml chess17\app\src\main\res\layout\
xcopy /Y app\src\main\res\menu\*.xml chess17\app\src\main\res\menu\
xcopy /Y app\src\main\res\xml\*.xml chess17\app\src\main\res\xml\

echo Files copied successfully!
pause
