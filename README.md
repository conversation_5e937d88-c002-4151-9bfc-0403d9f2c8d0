# Android Chess Game

A fully functional chess game for Android built with Kotlin.

## Features

- **Complete Chess Implementation**: All standard chess pieces with proper movement rules
- **Visual Chess Board**: Beautiful 8x8 chess board with alternating light and dark squares
- **Interactive Gameplay**: Touch to select pieces and make moves
- **Game State Management**: Tracks turns, check, checkmate, and stalemate conditions
- **Move Validation**: Ensures only legal moves are allowed
- **Visual Feedback**: 
  - Highlights selected pieces
  - Shows possible moves
  - Highlights king when in check
- **Game Controls**:
  - New Game button
  - Undo last move
  - Reset current game
- **Game End Detection**: Automatically detects and announces checkmate, stalemate, and draw conditions

## How to Play

1. **Starting a Game**: The game starts automatically with white pieces at the bottom
2. **Making Moves**: 
   - Tap a piece to select it (highlighted in yellow)
   - Possible moves are shown in green
   - Tap a destination square to move the piece
3. **Special Indicators**:
   - Red highlight indicates a king in check
   - Game status is displayed at the top
4. **Game Controls**:
   - **New Game**: Start a fresh game
   - **Undo**: Take back the last move
   - **Reset**: Reset the current game to starting position

## Technical Details

### Architecture
- **MainActivity**: Main activity handling UI interactions and game flow
- **ChessBoardView**: Custom view for rendering the chess board and handling touch events
- **ChessGame**: Core game logic including move validation and game state management
- **ChessPiece**: Data classes for chess pieces and positions

### Key Components
- **Move Validation**: Each piece type has specific movement rules implemented
- **Check Detection**: Automatically detects when kings are under attack
- **Game State Management**: Tracks current player, game status, and move history
- **Visual Rendering**: Custom drawing for the chess board with proper highlighting

## Requirements

- Android API level 24 (Android 7.0) or higher
- Android Studio for building and running

## Installation

1. Clone or download this project
2. Open in Android Studio
3. Build and run on an Android device or emulator

## Future Enhancements

Potential features that could be added:
- AI opponent with different difficulty levels
- Online multiplayer support
- Move history display
- Save/load game functionality
- Chess notation support
- Timer/clock functionality
- Sound effects
- Piece promotion animation
- Castling and en passant moves

## License

This project is open source and available under the MIT License.
