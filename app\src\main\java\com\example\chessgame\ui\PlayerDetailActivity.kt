package com.example.chessgame.ui

import android.content.Intent
import android.os.Bundle
import android.widget.Button
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.lifecycleScope
import com.example.chessgame.MainActivity
import com.example.chessgame.R
import com.example.chessgame.database.Player
import com.example.chessgame.viewmodel.ProfileViewModel
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.*

class PlayerDetailActivity : AppCompatActivity() {
    
    private lateinit var profileViewModel: ProfileViewModel
    private var currentPlayer: Player? = null
    
    private lateinit var tvPlayerName: TextView
    private lateinit var tvPlayerEmail: TextView
    private lateinit var tvPlayerRating: TextView
    private lateinit var tvGamesPlayed: TextView
    private lateinit var tvWinRate: TextView
    private lateinit var tvCreatedDate: TextView
    private lateinit var tvFavoriteColor: TextView
    private lateinit var btnPlayAsPlayer: Button
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_player_detail)
        
        setupViews()
        setupViewModel()
        loadPlayerData()
    }
    
    private fun setupViews() {
        tvPlayerName = findViewById(R.id.tvPlayerName)
        tvPlayerEmail = findViewById(R.id.tvPlayerEmail)
        tvPlayerRating = findViewById(R.id.tvPlayerRating)
        tvGamesPlayed = findViewById(R.id.tvGamesPlayed)
        tvWinRate = findViewById(R.id.tvWinRate)
        tvCreatedDate = findViewById(R.id.tvCreatedDate)
        tvFavoriteColor = findViewById(R.id.tvFavoriteColor)
        btnPlayAsPlayer = findViewById(R.id.btnPlayAsPlayer)
        
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        
        btnPlayAsPlayer.setOnClickListener {
            currentPlayer?.let { player ->
                val intent = Intent(this, MainActivity::class.java)
                intent.putExtra("SELECTED_PLAYER_ID", player.id)
                intent.putExtra("PLAYER_NAME", player.name)
                startActivity(intent)
                finish()
            }
        }
    }
    
    private fun setupViewModel() {
        profileViewModel = ViewModelProvider(this)[ProfileViewModel::class.java]
    }
    
    private fun loadPlayerData() {
        val playerId = intent.getLongExtra("PLAYER_ID", -1L)
        if (playerId == -1L) {
            finish()
            return
        }
        
        lifecycleScope.launch {
            currentPlayer = profileViewModel.getPlayerById(playerId)
            currentPlayer?.let { player ->
                displayPlayerData(player)
            } ?: run {
                finish()
            }
        }
    }
    
    private fun displayPlayerData(player: Player) {
        supportActionBar?.title = "${player.name}'s Profile"
        
        tvPlayerName.text = player.name
        tvPlayerEmail.text = player.email ?: "No email provided"
        tvPlayerRating.text = player.rating.toString()
        tvGamesPlayed.text = player.gamesPlayed.toString()
        
        val winRateText = String.format("%.1f%%", player.winRate)
        tvWinRate.text = winRateText
        
        val dateFormat = SimpleDateFormat("MMMM dd, yyyy", Locale.getDefault())
        tvCreatedDate.text = dateFormat.format(player.createdAt)
        
        tvFavoriteColor.text = if (player.favoriteColor == "WHITE") "White" else "Black"
    }
    
    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
