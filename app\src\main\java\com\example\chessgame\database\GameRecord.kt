package com.example.chessgame.database

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.ForeignKey
import java.util.Date

@Entity(
    tableName = "game_records",
    foreignKeys = [
        ForeignKey(
            entity = Player::class,
            parentColumns = ["id"],
            childColumns = ["whitePlayerId"],
            onDelete = ForeignKey.CASCADE
        ),
        ForeignKey(
            entity = Player::class,
            parentColumns = ["id"],
            childColumns = ["blackPlayerId"],
            onDelete = ForeignKey.CASCADE
        )
    ]
)
data class GameRecord(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val whitePlayerId: Long,
    val blackPlayerId: Long?,
    val gameResult: GameResult,
    val gameMode: GameMode,
    val startTime: Date,
    val endTime: Date?,
    val totalMoves: Int = 0,
    val gameDuration: Long = 0, // in milliseconds
    val moveHistory: String = "", // JSON string of moves
    val finalPosition: String = "" // FEN notation of final position
)

enum class GameResult {
    WHITE_WINS,
    BLACK_WINS,
    DRAW,
    STALEMATE,
    IN_PROGRESS,
    ABANDONED
}

enum class GameMode {
    SINGLE_PLAYER,
    TWO_PLAYER,
    ONLINE,
    AI_OPPONENT
}
