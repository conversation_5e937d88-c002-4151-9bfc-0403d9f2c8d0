package com.example.chessgame

class ChessGame {
    private val board = Array(8) { Array<ChessPiece?>(8) { null } }
    var currentPlayer = PieceColor.WHITE
        private set
    
    var gameState = GameState.PLAYING
        private set
    
    private var selectedPosition: Position? = null
    private val moveHistory = mutableListOf<Move>()
    
    enum class GameState {
        PLAYING, CHECK, CHECKMATE, STALEMATE, DRAW
    }
    
    data class Move(
        val from: Position,
        val to: Position,
        val piece: ChessPiece,
        val capturedPiece: ChessPiece? = null
    )
    
    init {
        initializeBoard()
    }
    
    private fun initializeBoard() {
        // Place pawns
        for (col in 0..7) {
            board[1][col] = ChessPiece(PieceType.PAWN, PieceColor.BLACK)
            board[6][col] = ChessPiece(PieceType.PAWN, PieceColor.WHITE)
        }
        
        // Place other pieces
        val pieceOrder = arrayOf(
            PieceType.ROOK, PieceType.KNIGHT, PieceType.BISHOP, PieceType.QUEEN,
            PieceType.KING, PieceType.BISHOP, PieceType.KNIGHT, PieceType.ROOK
        )
        
        for (col in 0..7) {
            board[0][col] = ChessPiece(pieceOrder[col], PieceColor.BLACK)
            board[7][col] = ChessPiece(pieceOrder[col], PieceColor.WHITE)
        }
    }
    
    fun getPiece(position: Position): ChessPiece? {
        return if (position.isValid()) board[position.row][position.col] else null
    }
    
    fun selectSquare(position: Position): Boolean {
        if (!position.isValid()) return false
        
        val piece = getPiece(position)
        
        // If no piece is selected
        if (selectedPosition == null) {
            if (piece?.color == currentPlayer) {
                selectedPosition = position
                return true
            }
            return false
        }
        
        // If same square is clicked, deselect
        if (selectedPosition == position) {
            selectedPosition = null
            return true
        }
        
        // Try to make a move
        val selectedPiece = getPiece(selectedPosition!!)
        if (selectedPiece != null && makeMove(selectedPosition!!, position)) {
            selectedPosition = null
            return true
        }
        
        // Select new piece if it belongs to current player
        if (piece?.color == currentPlayer) {
            selectedPosition = position
            return true
        }
        
        selectedPosition = null
        return false
    }
    
    fun getSelectedPosition(): Position? = selectedPosition
    
    fun getPossibleMoves(position: Position): List<Position> {
        val piece = getPiece(position) ?: return emptyList()
        val possibleMoves = mutableListOf<Position>()
        
        for (row in 0..7) {
            for (col in 0..7) {
                val targetPosition = Position(row, col)
                if (piece.canMoveTo(position, targetPosition, board)) {
                    // Check if move doesn't put own king in check
                    if (isMoveLegal(position, targetPosition)) {
                        possibleMoves.add(targetPosition)
                    }
                }
            }
        }
        
        return possibleMoves
    }
    
    private fun makeMove(from: Position, to: Position): Boolean {
        val piece = getPiece(from) ?: return false
        
        if (piece.color != currentPlayer) return false
        if (!piece.canMoveTo(from, to, board)) return false
        if (!isMoveLegal(from, to)) return false
        
        // Make the move
        val capturedPiece = getPiece(to)
        board[to.row][to.col] = piece
        board[from.row][from.col] = null
        piece.hasMoved = true
        
        // Record the move
        moveHistory.add(Move(from, to, piece, capturedPiece))
        
        // Switch players
        currentPlayer = if (currentPlayer == PieceColor.WHITE) PieceColor.BLACK else PieceColor.WHITE
        
        // Update game state
        updateGameState()
        
        return true
    }
    
    private fun isMoveLegal(from: Position, to: Position): Boolean {
        // Simulate the move
        val piece = board[from.row][from.col]
        val capturedPiece = board[to.row][to.col]
        
        board[to.row][to.col] = piece
        board[from.row][from.col] = null
        
        // Check if king is in check after the move
        val kingPosition = findKing(piece?.color ?: return false)
        val isLegal = kingPosition?.let { !isSquareUnderAttack(it, piece.color) } ?: false
        
        // Restore the board
        board[from.row][from.col] = piece
        board[to.row][to.col] = capturedPiece
        
        return isLegal
    }
    
    private fun findKing(color: PieceColor): Position? {
        for (row in 0..7) {
            for (col in 0..7) {
                val piece = board[row][col]
                if (piece?.type == PieceType.KING && piece.color == color) {
                    return Position(row, col)
                }
            }
        }
        return null
    }
    
    private fun isSquareUnderAttack(position: Position, defendingColor: PieceColor): Boolean {
        val attackingColor = if (defendingColor == PieceColor.WHITE) PieceColor.BLACK else PieceColor.WHITE
        
        for (row in 0..7) {
            for (col in 0..7) {
                val piece = board[row][col]
                if (piece?.color == attackingColor) {
                    if (piece.canMoveTo(Position(row, col), position, board)) {
                        return true
                    }
                }
            }
        }
        return false
    }
    
    private fun updateGameState() {
        val kingPosition = findKing(currentPlayer)
        val isInCheck = kingPosition?.let { isSquareUnderAttack(it, currentPlayer) } ?: false
        
        val hasLegalMoves = hasAnyLegalMoves(currentPlayer)
        
        gameState = when {
            isInCheck && !hasLegalMoves -> GameState.CHECKMATE
            !isInCheck && !hasLegalMoves -> GameState.STALEMATE
            isInCheck -> GameState.CHECK
            else -> GameState.PLAYING
        }
    }
    
    private fun hasAnyLegalMoves(color: PieceColor): Boolean {
        for (row in 0..7) {
            for (col in 0..7) {
                val piece = board[row][col]
                if (piece?.color == color) {
                    val fromPosition = Position(row, col)
                    if (getPossibleMoves(fromPosition).isNotEmpty()) {
                        return true
                    }
                }
            }
        }
        return false
    }
    
    fun resetGame() {
        // Clear board
        for (row in 0..7) {
            for (col in 0..7) {
                board[row][col] = null
            }
        }
        
        // Reset game state
        currentPlayer = PieceColor.WHITE
        gameState = GameState.PLAYING
        selectedPosition = null
        moveHistory.clear()
        
        // Initialize board
        initializeBoard()
    }
    
    fun getGameStatusText(): String {
        return when (gameState) {
            GameState.PLAYING -> if (currentPlayer == PieceColor.WHITE) "White's Turn" else "Black's Turn"
            GameState.CHECK -> if (currentPlayer == PieceColor.WHITE) "White in Check!" else "Black in Check!"
            GameState.CHECKMATE -> if (currentPlayer == PieceColor.WHITE) "Black Wins!" else "White Wins!"
            GameState.STALEMATE -> "Stalemate!"
            GameState.DRAW -> "Draw!"
        }
    }

    fun getMoveHistory(): List<Move> = moveHistory.toList()

    fun getMoveCount(): Int = moveHistory.size

    fun getLastMove(): Move? = moveHistory.lastOrNull()

    fun canUndo(): Boolean = moveHistory.isNotEmpty()

    fun undoLastMove(): Boolean {
        if (moveHistory.isEmpty()) return false

        val lastMove = moveHistory.removeAt(moveHistory.size - 1)

        // Restore the board state
        board[lastMove.from.row][lastMove.from.col] = lastMove.piece
        board[lastMove.to.row][lastMove.to.col] = lastMove.capturedPiece
        lastMove.piece.hasMoved = false // This is simplified - in a real game you'd need to track this properly

        // Switch back to previous player
        currentPlayer = if (currentPlayer == PieceColor.WHITE) PieceColor.BLACK else PieceColor.WHITE

        // Update game state
        updateGameState()

        return true
    }
}
