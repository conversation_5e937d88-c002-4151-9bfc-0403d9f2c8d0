package com.example.chessgame

enum class PieceType {
    PAWN, RO<PERSON>, KNIGHT, BISH<PERSON>, QUEEN, KING
}

enum class PieceColor {
    WHITE, B<PERSON>CK
}

data class Position(val row: Int, val col: Int) {
    fun isValid(): Boolean = row in 0..7 && col in 0..7
    
    operator fun plus(other: Position): Position = Position(row + other.row, col + other.col)
}

data class ChessPiece(
    val type: PieceType,
    val color: PieceColor,
    var hasMoved: Boolean = false
) {
    fun getSymbol(): String {
        return when (color) {
            PieceColor.WHITE -> when (type) {
                PieceType.KING -> "♔"
                PieceType.QUEEN -> "♕"
                PieceType.ROOK -> "♖"
                PieceType.BISHOP -> "♗"
                PieceType.KNIGHT -> "♘"
                PieceType.PAWN -> "♙"
            }
            PieceColor.BLACK -> when (type) {
                PieceType.KING -> "♚"
                PieceType.QUEEN -> "♛"
                PieceType.ROOK -> "♜"
                PieceType.BISHOP -> "♝"
                PieceType.KNIGHT -> "♞"
                PieceType.PAWN -> "♟"
            }
        }
    }
    
    fun canMoveTo(from: Position, to: Position, board: Array<Array<ChessPiece?>>): Boolean {
        if (!to.isValid() || from == to) return false
        
        val targetPiece = board[to.row][to.col]
        if (targetPiece?.color == this.color) return false
        
        return when (type) {
            PieceType.PAWN -> canPawnMoveTo(from, to, board)
            PieceType.ROOK -> canRookMoveTo(from, to, board)
            PieceType.KNIGHT -> canKnightMoveTo(from, to, board)
            PieceType.BISHOP -> canBishopMoveTo(from, to, board)
            PieceType.QUEEN -> canQueenMoveTo(from, to, board)
            PieceType.KING -> canKingMoveTo(from, to, board)
        }
    }
    
    private fun canPawnMoveTo(from: Position, to: Position, board: Array<Array<ChessPiece?>>): Boolean {
        val direction = if (color == PieceColor.WHITE) -1 else 1
        val startRow = if (color == PieceColor.WHITE) 6 else 1
        
        val rowDiff = to.row - from.row
        val colDiff = to.col - from.col
        
        // Forward move
        if (colDiff == 0) {
            if (rowDiff == direction && board[to.row][to.col] == null) return true
            if (from.row == startRow && rowDiff == 2 * direction && 
                board[to.row][to.col] == null && board[from.row + direction][from.col] == null) return true
        }
        // Diagonal capture
        else if (Math.abs(colDiff) == 1 && rowDiff == direction) {
            return board[to.row][to.col] != null
        }
        
        return false
    }
    
    private fun canRookMoveTo(from: Position, to: Position, board: Array<Array<ChessPiece?>>): Boolean {
        if (from.row != to.row && from.col != to.col) return false
        return isPathClear(from, to, board)
    }
    
    private fun canKnightMoveTo(from: Position, to: Position, board: Array<Array<ChessPiece?>>): Boolean {
        val rowDiff = Math.abs(to.row - from.row)
        val colDiff = Math.abs(to.col - from.col)
        return (rowDiff == 2 && colDiff == 1) || (rowDiff == 1 && colDiff == 2)
    }
    
    private fun canBishopMoveTo(from: Position, to: Position, board: Array<Array<ChessPiece?>>): Boolean {
        if (Math.abs(to.row - from.row) != Math.abs(to.col - from.col)) return false
        return isPathClear(from, to, board)
    }
    
    private fun canQueenMoveTo(from: Position, to: Position, board: Array<Array<ChessPiece?>>): Boolean {
        return canRookMoveTo(from, to, board) || canBishopMoveTo(from, to, board)
    }
    
    private fun canKingMoveTo(from: Position, to: Position, board: Array<Array<ChessPiece?>>): Boolean {
        val rowDiff = Math.abs(to.row - from.row)
        val colDiff = Math.abs(to.col - from.col)
        return rowDiff <= 1 && colDiff <= 1
    }
    
    private fun isPathClear(from: Position, to: Position, board: Array<Array<ChessPiece?>>): Boolean {
        val rowStep = when {
            to.row > from.row -> 1
            to.row < from.row -> -1
            else -> 0
        }
        val colStep = when {
            to.col > from.col -> 1
            to.col < from.col -> -1
            else -> 0
        }
        
        var currentRow = from.row + rowStep
        var currentCol = from.col + colStep
        
        while (currentRow != to.row || currentCol != to.col) {
            if (board[currentRow][currentCol] != null) return false
            currentRow += rowStep
            currentCol += colStep
        }
        
        return true
    }
}
