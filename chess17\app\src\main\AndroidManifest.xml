<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <application
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.ChessGame"
        tools:targetApi="31">
        <activity
            android:name=".ui.ProfileActivity"
            android:exported="true"
            android:theme="@style/Theme.ChessGame">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
        <activity
            android:name=".MainActivity"
            android:exported="false"
            android:theme="@style/Theme.ChessGame" />
            
        <activity
            android:name=".ui.CreateProfileActivity"
            android:exported="false"
            android:theme="@style/Theme.ChessGame"
            android:parentActivityName=".ui.ProfileActivity" />
            
        <activity
            android:name=".ui.PlayerDetailActivity"
            android:exported="false"
            android:theme="@style/Theme.ChessGame"
            android:parentActivityName=".ui.ProfileActivity" />
    </application>

</manifest>
