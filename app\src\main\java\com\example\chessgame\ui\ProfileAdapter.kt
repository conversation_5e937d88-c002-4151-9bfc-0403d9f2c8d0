package com.example.chessgame.ui

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageButton
import android.widget.TextView
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.chessgame.R
import com.example.chessgame.database.Player
import java.text.SimpleDateFormat
import java.util.*

class ProfileAdapter(
    private val onPlayerClick: (Player) -> Unit,
    private val onPlayAsClick: (Player) -> Unit,
    private val onDeleteClick: (Player) -> Unit
) : ListAdapter<Player, ProfileAdapter.ProfileViewHolder>(PlayerDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProfileViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_player_profile, parent, false)
        return ProfileViewHolder(view)
    }

    override fun onBindViewHolder(holder: ProfileViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ProfileViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val tvPlayerName: TextView = itemView.findViewById(R.id.tvPlayerName)
        private val tvPlayerEmail: TextView = itemView.findViewById(R.id.tvPlayerEmail)
        private val tvPlayerRating: TextView = itemView.findViewById(R.id.tvPlayerRating)
        private val tvPlayerStats: TextView = itemView.findViewById(R.id.tvPlayerStats)
        private val tvCreatedDate: TextView = itemView.findViewById(R.id.tvCreatedDate)
        private val btnPlayAs: Button = itemView.findViewById(R.id.btnPlayAs)
        private val btnDelete: ImageButton = itemView.findViewById(R.id.btnDelete)

        fun bind(player: Player) {
            tvPlayerName.text = player.name
            tvPlayerEmail.text = player.email ?: "No email"
            tvPlayerRating.text = "Rating: ${player.rating}"
            
            val statsText = "Games: ${player.gamesPlayed} | " +
                    "Wins: ${player.gamesWon} | " +
                    "Losses: ${player.gamesLost} | " +
                    "Draws: ${player.gamesDraw}"
            tvPlayerStats.text = statsText
            
            val dateFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
            tvCreatedDate.text = "Created: ${dateFormat.format(player.createdAt)}"
            
            // Set click listeners
            itemView.setOnClickListener { onPlayerClick(player) }
            btnPlayAs.setOnClickListener { onPlayAsClick(player) }
            btnDelete.setOnClickListener { onDeleteClick(player) }
            
            // Hide delete button for default players
            btnDelete.visibility = if (player.name == "Guest Player" || player.name == "Computer") {
                View.GONE
            } else {
                View.VISIBLE
            }
        }
    }

    class PlayerDiffCallback : DiffUtil.ItemCallback<Player>() {
        override fun areItemsTheSame(oldItem: Player, newItem: Player): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Player, newItem: Player): Boolean {
            return oldItem == newItem
        }
    }
}
