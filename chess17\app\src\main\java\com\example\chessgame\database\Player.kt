package com.example.chessgame.database

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.Date

@Entity(tableName = "players")
data class Player(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val name: String,
    val email: String? = null,
    val avatarPath: String? = null,
    val createdAt: Date = Date(),
    val gamesPlayed: Int = 0,
    val gamesWon: Int = 0,
    val gamesLost: Int = 0,
    val gamesDraw: Int = 0,
    val rating: Int = 1200, // Starting ELO rating
    val favoriteColor: String = "WHITE", // WHITE or BLACK
    val totalPlayTime: Long = 0 // in milliseconds
) {
    val winRate: Double
        get() = if (gamesPlayed > 0) (gamesWon.toDouble() / gamesPlayed.toDouble()) * 100 else 0.0
    
    val lossRate: Double
        get() = if (gamesPlayed > 0) (gamesLost.toDouble() / gamesPlayed.toDouble()) * 100 else 0.0
    
    val drawRate: Double
        get() = if (gamesPlayed > 0) (gamesDraw.toDouble() / gamesPlayed.toDouble()) * 100 else 0.0
}
